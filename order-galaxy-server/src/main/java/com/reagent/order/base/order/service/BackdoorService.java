package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.BackDoorRequestDTO;
import com.reagent.order.base.order.mapper.OrderExtraMapper;
import com.reagent.order.base.order.mapper.OrderUniqueBarCodeDOMapper;
import com.reagent.order.base.order.model.OrderExtraDO;
import com.reagent.order.base.order.model.OrderUniqueBarCodeDO;
import com.reagent.order.rpc.client.OrderMasterRPCClient;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.order.cache.RedisClient;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Liwenyu
 * @create: 2024-04-23 17:06
 * @description:
 */
@RestController
@RequestMapping("/backdoor")
public class BackdoorService {

    @Resource
    private OrderExtraMapper orderExtraMapper;

    @Resource
    private OrderMasterRPCClient orderMasterRPCClient;

    @Resource
    private OrderUniqueBarCodeDOMapper orderUniqueBarCodeDOMapper;



    /**
     * 初始化一物一码批次标签
     *
     * @return 是否成功
     */
    @PostMapping("/initFillBatchesTag")
    public RemoteResponse<Boolean> initFillBatchesTag(BackDoorRequestDTO backDoorRequestDTO) {

        Set<String> orderNoSet = backDoorRequestDTO.getOrderNos();
        if (CollectionUtils.isNotEmpty(orderNoSet)) {
            // 查哪些没有批次标签但是有一物一码数据的，补充标签
            List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderNoListAndExtraKey(orderNoSet, OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue());
            Set<String> existOrderNoSet = orderExtraDOList.stream().map(OrderExtraDO::getOrderNo).collect(Collectors.toSet());
            List<String> needInsertOrderNos = orderNoSet.stream().filter(orderNo -> !existOrderNoSet.contains(orderNo)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needInsertOrderNos)) {
                List<OrderMasterDTO> orderList = orderMasterRPCClient.findByOrderNoList(needInsertOrderNos);
                if (CollectionUtils.isNotEmpty(orderList)) {
                    List<OrderExtraDO> needInsertExtraList = orderList.stream().map(order -> {
                        OrderExtraDO orderExtraDO = new OrderExtraDO();
                        orderExtraDO.setOrderId(order.getId());
                        orderExtraDO.setOrderNo(order.getForderno());
                        orderExtraDO.setOrgId(order.getFuserid());
                        orderExtraDO.setExtraKey(OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue());
                        orderExtraDO.setExtraKeyDesc(OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getDesc());
                        orderExtraDO.setExtraValue("1");
                        return orderExtraDO;
                    }).collect(Collectors.toList());
                    orderExtraMapper.insertList(needInsertExtraList);
                }
            }
        }
        return RemoteResponse.success();
    }

    /**
     * 初始化一物一码批次标签
     *
     * @return 是否成功
     */
    @PostMapping("/initOneProductOneCodeTag")
    public RemoteResponse<Boolean> initOneProductOneCodeTag() {
        String minBarcode = null;
        int limit = 500;
        Set<String> orderNoSet = new HashSet<>(500);
        while(true){
            // 数据少于等于500，跳出
            List<OrderUniqueBarCodeDO> orderUniqueBarCodeDOList = orderUniqueBarCodeDOMapper.rangeQuery(null, minBarcode, limit);
            if(CollectionUtils.isEmpty(orderUniqueBarCodeDOList)){
                break;
            }
            for(OrderUniqueBarCodeDO orderUniqueBarCodeDO : orderUniqueBarCodeDOList){
                if(StringUtils.isNotEmpty(orderUniqueBarCodeDO.getOrderNo())){
                    orderNoSet.add(orderUniqueBarCodeDO.getOrderNo());
                }
            }
            if(CollectionUtils.isNotEmpty(orderNoSet)){
                // 查哪些没有批次标签但是有一物一码数据的，补充标签
                List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderNoListAndExtraKey(orderNoSet, OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
                Set<String> existOrderNoSet = orderExtraDOList.stream().map(OrderExtraDO::getOrderNo).collect(Collectors.toSet());
                List<String> needInsertOrderNos = orderNoSet.stream().filter(orderNo->!existOrderNoSet.contains(orderNo)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(needInsertOrderNos)){
                    List<OrderMasterDTO> orderList = orderMasterRPCClient.findByOrderNoList(needInsertOrderNos);
                    orderList = orderList.stream().filter(order->order.getFuserid() == 94).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(orderList)){
                        List<OrderExtraDO> needInsertExtraList = orderList.stream()
                                .map(order->{
                            OrderExtraDO orderExtraDO = new OrderExtraDO();
                            orderExtraDO.setOrderId(order.getId());
                            orderExtraDO.setOrderNo(order.getForderno());
                            orderExtraDO.setOrgId(order.getFuserid());
                            orderExtraDO.setExtraKey(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
                            orderExtraDO.setExtraKeyDesc(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getDesc());
                            orderExtraDO.setExtraValue("1");
                            return orderExtraDO;
                        }).collect(Collectors.toList());
                        orderExtraMapper.insertList(needInsertExtraList);
                    }
                }
                orderNoSet.clear();
            }
            if(orderUniqueBarCodeDOList.size() < limit){
                break;
            }
            minBarcode = orderUniqueBarCodeDOList.get(orderUniqueBarCodeDOList.size() - 1).getBarCode();
        }
        return RemoteResponse.success();
    }

    @Resource
    private RedisClient redisClient;


    /**
     * 测试方法：验证获取锁超时时的行为
     * 测试场景：
     * 1. 先获取一个锁并持有
     * 2. 再次尝试获取同一个锁，观察是否抛异常还是返回false
     */
    @PostMapping("/testLockTimeout")
    public void testLockTimeout() {
        String testLockKey = "test_lock_timeout_" + System.currentTimeMillis();

        System.out.println("=== 开始测试锁超时行为 ===");

        try {
            // 第一步：获取锁并持有
            System.out.println("1. 尝试获取锁: " + testLockKey);
            boolean firstLock = redisClient.lockWithWait(testLockKey, 10, 1); // 持有10秒，等待1秒
            System.out.println("第一次获取锁结果: " + firstLock);

            if (firstLock) {
                // 第二步：在另一个线程中尝试获取同一个锁
                Thread testThread = new Thread(() -> {
                    try {
                        System.out.println("2. 在新线程中尝试获取同一个锁...");
                        long startTime = System.currentTimeMillis();

                        boolean secondLock = redisClient.lockWithWait(testLockKey, 5, 2); // 等待2秒

                        long endTime = System.currentTimeMillis();
                        long waitTime = endTime - startTime;

                        System.out.println("第二次获取锁结果: " + secondLock);
                        System.out.println("实际等待时间: " + waitTime + "ms");

                        if (!secondLock) {
                            System.out.println("✅ 锁超时返回false，没有抛异常");
                        } else {
                            System.out.println("❌ 意外获取到锁");
                        }

                    } catch (Exception e) {
                        System.out.println("❌ 获取锁时抛出异常: " + e.getClass().getSimpleName() + " - " + e.getMessage());
                        e.printStackTrace();
                    }
                });

                testThread.start();

                // 等待测试线程完成
                Thread.sleep(5000);

                // 第三步：释放第一个锁
                System.out.println("3. 释放第一个锁");
                boolean unlocked = redisClient.unlockWithWait(testLockKey);
                System.out.println("释放锁结果: " + unlocked);

                testThread.join(1000);

            } else {
                System.out.println("❌ 第一次获取锁失败");
            }

        } catch (Exception e) {
            System.out.println("❌ 测试过程中发生异常: " + e.getClass().getSimpleName() + " - " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("=== 锁超时行为测试完成 ===");
    }
}
