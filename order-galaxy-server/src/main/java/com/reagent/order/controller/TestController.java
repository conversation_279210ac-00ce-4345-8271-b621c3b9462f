package com.reagent.order.controller;

import com.reagent.order.base.order.service.OrderExtraRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private OrderExtraRpcService orderExtraRpcService;

    /**
     * 测试锁超时行为
     */
    @GetMapping("/lockTimeout")
    public RemoteResponse<String> testLockTimeout() {
        return orderExtraRpcService.testLockTimeout();
    }
}
